package com.fs.swap.wx.controller;

import cn.binarywang.wx.miniapp.api.WxMaSecurityService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import com.fs.swap.common.constant.Constants;
import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.*;
import com.fs.swap.common.core.domain.model.WxLoginUser;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.core.redis.RedisCache;
import com.fs.swap.common.enums.*;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.common.green.ContentModerationFacade;
import com.fs.swap.common.mapstruct.ConvertUtil;
import com.fs.swap.common.utils.ip.AddressUtils;
import com.fs.swap.common.utils.ip.IpUtils;
import com.fs.swap.system.domain.vo.UserInfoVO;
import com.fs.swap.system.domain.vo.UserSilverInfoVO;
import com.fs.swap.system.service.*;
import com.fs.swap.wx.pojo.dto.UserContactDTO;
import com.fs.swap.wx.pojo.dto.UserInfoDTO;
import com.fs.swap.wx.pojo.dto.WxLoginInfoDTO;
import com.fs.swap.wx.pojo.vo.UserContactVO;
import com.fs.swap.wx.service.NameGenerator;
import com.fs.swap.wx.service.TokenService;
import com.google.common.base.Strings;
import me.chanjar.weixin.common.error.WxErrorException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fs.swap.common.constant.CacheConstants.WATCH_AD_COUNT;
import static com.fs.swap.common.constant.SilverConstants.*;
import static com.fs.swap.common.enums.ContactType.MOBILE;


/**
 * 菜单信息
 */
@RestController
@RequestMapping("/user")
public class UserController extends WxApiBaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private WxMaService wxMaService;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IUserLoginRecordService userLoginRecordService;

    @Autowired
    private TokenService tokenService;
    @Value("${dromara.fileDomain}")
    private String fileUrl;
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Autowired
    private IUserInviteRecordService userInviteRecordService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private IUserSilverRecordService userSilverRecordService;
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IWxIdInfoService wxIdInfoService;
    @Autowired
    private IUserHomeService userHomeService;
    @Autowired
    private IResidentialAreaService residentialAreaService;
    @Autowired
    private IProductService productService;
    @Autowired
    private IUserCollectionService userCollectionService;
    @Autowired
    private ITradeOrderService tradeOrderService;
    @Autowired
    private IUserFollowService userFollowService;
    @Autowired
    private WxMaSecurityService wxMaSecurityService;
    @Autowired
    private ContentModerationFacade contentModerationFacade;
    @Autowired
    private IUserContactService userContactService;

    private static final int MAX_WATCH_AD_COUNT = 5;

    /**
     * 手机号快速登录
     */
    @PostMapping(value = "/login_by_phone")
    @ResponseBody
    @Transactional
    public AjaxResult loginByPhone(@RequestBody WxLoginInfoDTO wxLoginInfoDTO) {
        logger.info("用户登录");
        WxMaPhoneNumberInfo phoneInfo;
        WxMaJscode2SessionResult sessionInfo;
        try {
            sessionInfo = wxMaService.getUserService().getSessionInfo(wxLoginInfoDTO.getLoginCode());
            phoneInfo = wxMaService.getUserService().getPhoneNumber(wxLoginInfoDTO.getPhoneCode());
            WxMaConfigHolder.remove();
            if (phoneInfo == null) {
                throw new ServiceException(ErrorType.E_5001);
            }
        } catch (WxErrorException e) {
            throw new ServiceException(e.getMessage());
        }
        UserInfo userInfo = userInfoService.selectUserInfoByMobile(phoneInfo.getPurePhoneNumber());

        if (userInfo == null) {
            //注册为新用户
            userInfo = new UserInfo();
            userInfo.setMobile(phoneInfo.getPurePhoneNumber());
            userInfo.setCreateTime(new Date());
            userInfo.setNickname(NameGenerator.getRandomName());
            userInfo.setSlogan("赠人玫瑰，手有余香");
            userInfo.setUnionId(sessionInfo.getUnionid());
            userInfoService.insertUserInfo(userInfo);


            // 添加用户联系信息，手机号为当前手机号，默认不可见
            userContactService.saveUserContact(
                    userInfo.getId(),
                    MOBILE.getCode(),
                    phoneInfo.getPurePhoneNumber(),
                    false
            );

            userInfoService.addSilverAndRecord(userInfo.getId(), REGISTER, SilverEventType.REGISTER);

            // 如果登录时传入了小区ID，自动绑定用户小区
            if (wxLoginInfoDTO.getResidentialId() != null) {
                try {
                    userHomeService.bindUserHome(userInfo.getId(), wxLoginInfoDTO.getResidentialId());
                } catch (Exception e) {
                    // 绑定小区失败不影响登录流程，记录日志即可
                    logger.warn("自动绑定用户小区失败，用户ID: {}, 小区ID: {}", userInfo.getId(), wxLoginInfoDTO.getResidentialId(), e);
                }
            }

            if (wxLoginInfoDTO.getInviteUserId() != null) {
                if (InviteType.REGISTER.val().equals(wxLoginInfoDTO.getInviteType())) {
                    UserInviteRecord us = new UserInviteRecord();
                    us.setInviterUserId(wxLoginInfoDTO.getInviteUserId());
                    us.setInviteeUserId(userInfo.getId());
                    threadPoolTaskExecutor.execute(() -> userInviteRecordService.registerUserInviteSuccess(us));
                }
            }
        } else {
            userInfo.setUnionId(sessionInfo.getUnionid());
            userInfoService.updateUserInfo(userInfo);
        }
        //unionId - openid
        WxIdInfo wxIdInfo = wxIdInfoService.selectWxIdInfoByUnionId(sessionInfo.getUnionid());
        if (wxIdInfo == null) {
            wxIdInfo = new WxIdInfo();
            wxIdInfo.setUnionId(sessionInfo.getUnionid());
            wxIdInfo.setMaOpenId(sessionInfo.getOpenid());
            wxIdInfoService.insertWxIdInfo(wxIdInfo);
        } else {
            wxIdInfo.setMaOpenId(sessionInfo.getOpenid());
            wxIdInfoService.updateWxIdInfo(wxIdInfo);
        }

        WxLoginUser wxLoginUser = new WxLoginUser();
        wxLoginUser.setUserId(userInfo.getId());
        wxLoginUser.setUser(userInfo);
        String token = tokenService.createToken(wxLoginUser);
        //记录登录日志 TODO异步
//        loginRecord(userInfo.getId());
        AjaxResult ajax = AjaxResult.success();
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    @GetMapping("/check_login")
    public AjaxResult checkLogin() {
        UserInfo userInfo = userInfoService.selectUserInfoById(getUserId());
        if (userInfo == null) {
            throw new ServiceException(ErrorType.E_5002);
        }
        return AjaxResult.success();
    }

    /**
     * 静默登录
     */
    @PostMapping(value = "/login_by_wechat")
    @ResponseBody
    @Transactional
    public AjaxResult loginByWechat(@RequestBody WxLoginInfoDTO wxLoginInfoDTO) {
        WxMaJscode2SessionResult sessionInfo;
        try {
            sessionInfo = wxMaService.getUserService().getSessionInfo(wxLoginInfoDTO.getLoginCode());
            WxMaConfigHolder.remove();
        } catch (WxErrorException e) {
            throw new ServiceException(e.getMessage());
        }
        UserInfo userInfo = userInfoService.selectUserInfoByUnionId(sessionInfo.getUnionid());

        if (userInfo != null) {
            WxIdInfo wxIdInfo = wxIdInfoService.selectWxIdInfoByUnionId(sessionInfo.getUnionid());
            wxIdInfo.setMaOpenId(sessionInfo.getOpenid());
            wxIdInfoService.updateWxIdInfo(wxIdInfo);

            WxLoginUser wxLoginUser = new WxLoginUser();
            wxLoginUser.setUserId(userInfo.getId());
            wxLoginUser.setUser(userInfo);
            String token = tokenService.createToken(wxLoginUser);

            AjaxResult ajax = AjaxResult.success();
            ajax.put(Constants.TOKEN, token);
            return ajax;
        }
        return AjaxResult.success();

    }


    private void loginRecord(long userId) {
        UserLoginRecord userLoginRecord = new UserLoginRecord();
        userLoginRecord.setUserId(userId);
        String ip = IpUtils.getIpAddr();
        userLoginRecord.setIp(ip);
        userLoginRecord.setLocation(AddressUtils.getRealAddressByIP(ip));
        userLoginRecord.setCreateTime(new Date());
        userLoginRecordService.insertUserLoginRecord(userLoginRecord);
    }

    @GetMapping("/user_info")
    public AjaxResult getUserInfo() {
        Long userId = getUserId();
        UserInfo userInfo = userInfoService.selectUserInfoById(userId);
        UserInfoVO convert = ConvertUtil.convert(userInfo, UserInfoVO.class);
        if (!Strings.isNullOrEmpty(userInfo.getAvatar())) {
            convert.setAvatar(fileUrl + convert.getAvatar());
        }
        WxIdInfo wxIdInfo = wxIdInfoService.selectWxIdInfoByUnionId(userInfo.getUnionId());
        convert.setSubscribeMpIs(!Strings.isNullOrEmpty(wxIdInfo.getMpOpenId()));
        UserHome userHome = userHomeService.selectUserSelectedHome(userId);
        if (userHome != null) {
            ResidentialArea residentialArea = residentialAreaService.selectResidentialAreaById(userHome.getResidentialId());
            convert.setCurrentResidentialId(residentialArea.getId());
            convert.setCurrentResidentialName(residentialArea.getName());
            convert.setResidentialCertificationStatus(userHome.getCertificationStatus());
            convert.setCurrentResidentialAddress(residentialArea.getAddress());
        } else {
            convert.setCurrentResidentialId(0L);
            convert.setCurrentResidentialName("未绑定小区");
        }
        return AjaxResult.success(convert);
    }

    /**
     * 获取指定用户的公开信息
     *
     * @param userId 用户ID
     * @return 用户公开信息
     */
    @GetMapping("/user_public_info/{userId}")
    public AjaxResult getUserPublicInfo(@PathVariable Long userId) {
        // 获取用户信息
        UserInfo userInfo = userInfoService.selectUserInfoById(userId);
        if (userInfo == null) {
            return AjaxResult.error("用户不存在");
        }

        // 创建返回结果对象，只包含公开信息
        Map<String, Object> publicInfo = new HashMap<>();
        publicInfo.put("id", userInfo.getId());
        publicInfo.put("nickname", userInfo.getNickname());
        if (!Strings.isNullOrEmpty(userInfo.getAvatar())) {
            publicInfo.put("avatar", fileUrl + userInfo.getAvatar());
        }
        publicInfo.put("slogan", userInfo.getSlogan());

        // 获取当前登录用户ID
        Long currentUserId = getUserId();

        // 检查是否已关注该用户
        boolean isFollowed = false;
        if (currentUserId != null && !currentUserId.equals(userId)) {
            UserFollow userFollow = userFollowService.selectUserFollowByUserIdAndFollowUserId(currentUserId, userId);
            isFollowed = (userFollow != null);
        }
        publicInfo.put("isFollowed", isFollowed);

        // 获取关注数和粉丝数
        int followCount = userFollowService.selectFollowCount(userId);
        int fansCount = userFollowService.selectFansCount(userId);
        publicInfo.put("followCount", followCount);
        publicInfo.put("fansCount", fansCount);

        return AjaxResult.success(publicInfo);
    }

    @GetMapping("/silver_info")
    public AjaxResult getUserSilverInfo() {
        Long userId = getUserId();
        UserInfo userInfo = userInfoService.selectUserInfoById(userId);

        Long free = userSilverRecordService.selectTotalSilverByUserIdAndEvents(
                userId,
                SilverEventType.getFreeEventTypes()
        );

        return AjaxResult.success(UserSilverInfoVO.builder()
                .remainingSilver(userInfo.getSilver())
                .freeGetSilver(free)
                .build());
    }


    @GetMapping("/watch_ad")
    public AjaxResult watchAd() {
        Long userId = getUserId();
        String key = String.format(WATCH_AD_COUNT, userId);

        // 获取今日观看次数
        Integer count = redisCache.getCacheObject(key);
        if (count != null && count > MAX_WATCH_AD_COUNT) {
            throw new ServiceException(ErrorType.E_5017);
        }
        // 增加观看次数
        if (count == null) {
            redisCache.setCacheObject(key, 1, 24, TimeUnit.HOURS);
        } else {
            redisCache.setCacheObject(key, count + 1, 24, TimeUnit.HOURS);
        }
        userInfoService.addSilverAndRecord(userId, WATCH_AD, SilverEventType.AD);
        return AjaxResult.success();
    }

    @GetMapping("/silver_detail")
    public TableDataInfo getUserSilverDetail(UserSilverRecord userSilverRecord) {
        startPage();
        userSilverRecord.setUserId(getUserId());
        List<UserSilverRecord> list = userSilverRecordService.selectUserSilverRecordList(userSilverRecord);
        return getDataTable(list);
    }

    @PatchMapping("/user_info")
    public AjaxResult updateUserInfo(@RequestBody UserInfoDTO userInfoDTO) {
        UserInfo userInfo = ConvertUtil.convert(userInfoDTO, UserInfo.class);
        userInfo.setId(getUserId());
        UserInfo userInfoOld = userInfoService.selectUserInfoById(getUserId());

        if (userInfo.getNickname() != null && !userInfoOld.getNickname().equals(userInfo.getNickname())) {
            String nickname = userInfo.getNickname();
            // 验证昵称长度
            int length = nickname.codePointCount(0, nickname.length());
            if (length < 2 || length > 14) {
                throw new ServiceException(ErrorType.E_5016);
            }
            // 验证首字符
            if (!nickname.substring(0, 1).matches("^[a-zA-Z0-9\\u4e00-\\u9fa5]")) {
                throw new ServiceException(ErrorType.E_5016);
            }
            // 验证昵称格式
            if (!nickname.matches("^[a-zA-Z0-9\\u4e00-\\u9fa5_.-]+$")) {
                throw new ServiceException(ErrorType.E_5016);
            }
            contentModerationFacade.checkText(userInfo.getNickname(), GreenTextType.NICKNAME_DETECTION.val());
        }
        userInfoService.updateUserInfo(userInfo);
        return AjaxResult.success();
    }

    /**
     * 获取用户统计数据
     */
    @GetMapping("/stats")
    public AjaxResult getUserStats() {
        Long userId = getUserId();

        // 创建返回结果对象
        Map<String, Object> statsMap = new HashMap<>();

        try {
            // 1. 查询用户发布的商品数量
            Product productQuery = new Product();
            productQuery.setUserId(userId);
            productQuery.setStatus(ProductStatus.ON_SALE.val());
            List<Product> products = productService.selectProductList(productQuery);
            statsMap.put("productCount", products.size());

            // 2. 查询用户收藏的商品数量
            UserCollection collectionQuery = new UserCollection();
            collectionQuery.setUserId(userId);
            List<UserCollection> collections = userCollectionService.selectUserCollectionList(collectionQuery);
            statsMap.put("collectionCount", collections.size());

            // 3. 查询用户卖出的商品数量
            TradeOrder soldQuery = new TradeOrder();
            soldQuery.setSellerId(userId);
            List<TradeOrder> soldOrders = tradeOrderService.selectTradeOrderList(soldQuery);
            statsMap.put("soldCount", soldOrders.size());

            // 4. 查询用户买入的商品数量
            TradeOrder boughtQuery = new TradeOrder();
            boughtQuery.setBuyerId(userId);
            List<TradeOrder> boughtOrders = tradeOrderService.selectTradeOrderList(boughtQuery);
            statsMap.put("boughtCount", boughtOrders.size());

            // 5. 查询用户关注数量和粉丝数量
            int followCount = userFollowService.selectFollowCount(userId);
            int fansCount = userFollowService.selectFansCount(userId);
            statsMap.put("followCount", followCount);
            statsMap.put("fansCount", fansCount);

            return AjaxResult.success(statsMap);
        } catch (Exception e) {
            logger.error("获取用户统计数据失败", e);
            return AjaxResult.error("获取用户统计数据失败");
        }
    }

    /**
     * 修改slogan
     */
    @PostMapping("/change_slogan")
    @Transactional
    public AjaxResult changeSlogan(@RequestParam("slogan") String slogan) {

        UserInfo userInfo = userInfoService.selectUserInfoById(getUserId());
        if (userInfo.getSilver() < SLOGAN_COST) {
            throw new ServiceException(ErrorType.E_5009);
        }
        contentModerationFacade.checkText(slogan, GreenTextType.COMMENT_DETECTION.val());
        userInfo.setSilver(userInfo.getSilver() - SLOGAN_COST);
        userInfo.setSlogan(slogan);

        UserSilverRecord build = UserSilverRecord.builder().type(SilverChangeType.REDUCE.val()).userId(getUserId()).event(SilverEventType.SLOGAN.val())
                .number(SLOGAN_COST).build();
        userSilverRecordService.insertUserSilverRecord(build);
        userInfoService.updateUserInfo(userInfo);
        return AjaxResult.success();
    }

    /**
     * 获取用户联系方式列表
     */
    @GetMapping("/contacts")
    public AjaxResult getUserContacts() {
        Long userId = getUserId();
        List<UserContact> contacts = userContactService.selectUserContactsByUserId(userId);

        List<UserContactVO> contactVOs = contacts.stream().map(contact -> {
            UserContactVO vo = new UserContactVO();
            vo.setContactType(contact.getContactType());
            vo.setContactValue(contact.getContactValue());
            vo.setIsVisible(contact.getIsVisible());
            return vo;
        }).collect(Collectors.toList());

        return AjaxResult.success(contactVOs);
    }

    /**
     * 更新用户联系方式
     */
    @PostMapping("/contact")
    public AjaxResult updateUserContact(@RequestBody UserContactDTO contactDTO) {
        Long userId = getUserId();

        // 验证联系方式类型
        if (!isValidContactType(contactDTO.getContactType())) {
            return AjaxResult.error("无效的联系方式类型");
        }

        // 验证联系方式值
        if (contactDTO.getContactValue() != null && !contactDTO.getContactValue().isEmpty()) {
            if ("1".equals(contactDTO.getContactType())) {
                // 手机号验证
//                if (!contactDTO.getContactValue().matches("^1[3-9]\\d{9}$")) {
//                    return AjaxResult.error("手机号格式不正确");
//                }
                throw new ServiceException(ErrorType.E_5021);
            } else if ("2".equals(contactDTO.getContactType())) {
                // 微信号验证
                if (!contactDTO.getContactValue().matches("^[a-zA-Z0-9_-]{6,20}$")) {
                    return AjaxResult.error("微信号格式不正确，应为6-20个字符，只能包含字母、数字、下划线和减号");
                }
            }
        }

        // 保存联系方式
        int result = userContactService.saveUserContact(
                userId,
                contactDTO.getContactType(),
                contactDTO.getContactValue(),
                contactDTO.getIsVisible()
        );

        if (result > 0) {
            return AjaxResult.success("联系方式更新成功");
        } else {
            return AjaxResult.error("联系方式更新失败");
        }
    }

    /**
     * 更新联系方式可见性
     */
    @PutMapping("/contact/visibility")
    public AjaxResult updateContactVisibility(@RequestBody UserContactDTO contactDTO) {
        Long userId = getUserId();

        // 验证联系方式类型
        if (!isValidContactType(contactDTO.getContactType())) {
            return AjaxResult.error("无效的联系方式类型");
        }

        // 更新可见性
        int result = userContactService.updateContactVisibility(
                userId,
                contactDTO.getContactType(),
                contactDTO.getIsVisible()
        );

        if (result > 0) {
            return AjaxResult.success("联系方式可见性更新成功");
        } else {
            return AjaxResult.error("联系方式可见性更新失败，请确认该联系方式已设置");
        }
    }

    /**
     * 验证联系方式类型是否有效
     */
    private boolean isValidContactType(String contactType) {
        return ContactType.isValid(contactType);
    }
}